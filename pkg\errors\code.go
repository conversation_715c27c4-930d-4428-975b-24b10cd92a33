package errors

import "errors"

var NoneError = errors.New("user error,don't need log ")

// 系统错误  -1 ~ -∞ 不展示给用户
const (
	SYSTEM_ERROR                 = -1
	MAKE_DIR_ERROR               = -2
	CREATE_FILE_ERROR            = -3
	WRITE_FILE_ERROR             = -4
	OPEN_PART_ERROR              = -5
	DATABASE_WRITE_ERROR         = -6
	READ_UPLOAD_ERROR            = -7
	CREATE_RECORD_ERROR          = -8
	GET_RECORD_ERROR             = -9
	SEND_INTERNAL_HTTP_ERROR     = -10
	INTERNAL_HTTP_RESPONSE_ERROR = -11
	JSON_UNMARSHAL_ERROR         = -12
	JSON_MARSHAL_ERROR           = -12
	GET_TAG_ERROR                = -13
	UPDATE_RECORD_ERROR          = -14
)

// 成功占用CODE 0-9999
const (
	SUCCESS = iota
	HAVE_DATA_SUCCESS
)

// 可展示给用户的错误 10000 ~ 无穷
const (
	INVALID_PARAMETER            = 10000
	INVALID_TOKEN                = 10001
	INVALID_USER                 = 10002
	INVALID_PASSWORD             = 10003
	OVERSIZE_FILE                = 10004
	INVALID_FILETYPE             = 10005
	FILE_NOT_FOUND               = 10006
	PERMISSION_DENIED            = 10007
	FILE_NOT_AVAILABLE           = 10008
	PART_ALREADY_EXISTS          = 10009
	UPLOAD_HAVE_OMIT             = 10010
	CATEGORY_ERROR               = 10011
	TITLE_ERROR                  = 10012
	TYPE_ERROR                   = 10013
	VIDEO_ID_ERROR               = 10014
	FILE_ID_ERROR                = 10015
	COVER_ERROR                  = 10016
	MEMBERS_ERROR                = 10017
	PUBLISHER_ERROR              = 10018
	MAINCREATOR_ERROR            = 10019
	FILE_NOT_UPLOADED            = 10020
	NOT_USER_FILE                = 10021
	NOT_VALID_CATEGORY           = 10022
	VIDEO_ID_EXISTS              = 10023
	GET_TAG_NOT_EXISTS_ERROR     = 10024
	VIDEO_ID_FORMAT_ERROR        = 10083
	ORIGINAL_TAGS_ERROR          = 10082
	DATA_NOT_FOUND               = 10025
	CONTENT_STATUS_ERROR         = 10026
	NO_AVAILABLE_TASK            = 10027
	ALREADY_VOTED                = 10028
	PUBLISH_AT_ERROR             = 10029
	DO_NOT_HAVE_THIS_TRANSLATION = 10030
	SERVICE_UNAVAILABLE          = 10031

	// 媒体处理服务相关错误
	NOT_FOUND              = 10032
	INTERNAL_SERVER_ERROR  = 10033
	MEDIA_PROCESSING_ERROR = 10034
	TASK_NOT_FOUND         = 10035
	INVALID_MEDIA_FORMAT   = 10036
	PROCESSING_TIMEOUT     = 10037
	STORAGE_ERROR          = 10038

	CREATED_TIME_ERROR                   = 10039
	OUT_OF_MAX_PARTS_NUM                 = 10040
	CREATE_UPLOAD_BUCKET_PARTS_ERROR     = 10041
	CANCEL_UPLOAD_BUCKET_PARTS_ERROR     = 10042
	CREATE_UPLOAD_BUCKET_PARTS_URL_ERROR = 10043
	UPLOAD_PART_PRESIGN_URL_NOT_FOUND    = 10044
	INVALID_FILE_STATUS                  = 10045
	AUDIT_TASK_STATUS_NOT_IS_PENDING     = 10046
	AVATAR_FILE_NOT_FOUND                = 10047
	UPLOAD_AVATAR_DISABLED               = 10048
	AVATAR_FILE_SIZE_EXCEEDED            = 10049
	DECODE_IMAGE_ERROR                   = 10050
	AVATAR_DIMENSION_TOO_SMALL           = 10051
	UPLOAD_AVATAR_FAILED                 = 10052
	READUPLOAD_ERROR                     = 10053

	// Banner相关错误
	UPLOAD_BANNER_DISABLED     = 10054
	UPLOAD_BANNER_FAILED       = 10055
	BANNER_FILE_NOT_FOUND      = 10056
	BANNER_FILE_SIZE_EXCEEDED  = 10057
	BANNER_DIMENSION_TOO_SMALL = 10058

	COLLABORATOR_NOT_FOUND = 10059
	TRANSACTION_ERROR      = 10060

	INVALID_COLLABORATOR_PARAMETER  = 10061
	INVALID_COLLABORATOR_USER_KSUID = 10062
	INCOME_ALLOCATION_ERROR         = 10063
	COVER_OR_VIDEOFILE_USED         = 10064

	RECORD_ALREADY_PROCESSED = 10065
	DELETE_RECORD_ERROR      = 10066
	NOT_ANIME_CATEGORY       = 10067 // 不是动漫分类
	SECRET_USER_LIKE         = 10068
	SECRET_USER_FAVORITE     = 10069

	// 合集相关错误
	ALBUM_NOT_FOUND          = 10070 // 合集不存在
	ALBUM_NAME_EXISTS        = 10071 // 合集名称已存在
	ALBUM_LIMIT_EXCEEDED     = 10072 // 用户合集数量超限
	CONTENT_TYPE_MISMATCH    = 10074 // 内容类型不匹配
	CONTENT_NOT_FOUND        = 10075 // 内容不存在
	CONTENT_ALREADY_IN_ALBUM = 10076 // 内容已在合集中
	CONTENT_NOT_IN_ALBUM     = 10077 // 内容不在合集中
	INVALID_ALBUM_TYPE       = 10078 // 无效的合集类型
	INVALID_CONTENT_TYPE     = 10079 // 无效的内容类型
)

// 常用错误码别名
const (
	INVALID_PARAMS = INVALID_PARAMETER
	USER_NOT_FOUND = INVALID_USER
	INTERNAL_ERROR = SYSTEM_ERROR
)
