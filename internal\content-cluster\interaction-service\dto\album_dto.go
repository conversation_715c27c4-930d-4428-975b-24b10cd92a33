package dto

import "time"

// CreateAlbumRequest 创建合集请求
type CreateAlbumRequest struct {
	UserKSUID   string `json:"-"`                                                                 // 从JWT中获取
	AlbumName   string `json:"album_name" binding:"required,min=1,max=100"`                       // 合集名称
	AlbumType   string `json:"album_type" binding:"required,oneof=video short_video anime novel"` // 合集类型
	CoverKSUID  string `json:"cover_ksuid" binding:"omitempty,len=32"`                            // 封面KSUID（可选）
	IsPublic    bool   `json:"is_public"`                                                         // 是否公开
	Description string `json:"description" binding:"max=500"`                                     // 合集描述
}

// CreateAlbumResponse 创建合集响应
type CreateAlbumResponse struct {
	AlbumKSUID   string             `json:"album_ksuid"`
	AlbumName    string             `json:"album_name"`
	AlbumType    string             `json:"album_type"`
	CoverKSUID   string             `json:"cover_ksuid,omitempty"`
	CoverURL     string             `json:"cover_url,omitempty"`
	IsPublic     bool               `json:"is_public"`
	Description  string             `json:"description"`
	ContentCount int                `json:"content_count"`
	Contents     []AlbumContentItem `json:"contents"`
	CreatedAt    time.Time          `json:"created_at"`
}

// UpdateAlbumRequest 更新合集请求
type UpdateAlbumRequest struct {
	UserKSUID   string `json:"-"` // 从JWT中获取
	AlbumKSUID  string `json:"-"` // 从路径参数获取
	AlbumName   string `json:"album_name" binding:"required,min=1,max=100"`
	Description string `json:"description" binding:"max=500"`
	IsPublic    bool   `json:"is_public"`
}

// UpdateAlbumResponse 更新合集响应
type UpdateAlbumResponse struct {
	AlbumKSUID   string             `json:"album_ksuid"`
	AlbumName    string             `json:"album_name"`
	AlbumType    string             `json:"album_type"`
	IsPublic     bool               `json:"is_public"`
	Description  string             `json:"description"`
	ContentCount int                `json:"content_count"`
	Contents     []AlbumContentItem `json:"contents"`
	UpdatedAt    time.Time          `json:"updated_at"`
}

// GetAlbumRequest 获取合集请求
type GetAlbumRequest struct {
	UserKSUID  string `json:"-"` // 从JWT中获取（可选，用于权限检查）
	AlbumKSUID string `json:"-"` // 从路径参数获取
}

// GetAlbumResponse 获取合集响应
type GetAlbumResponse struct {
	AlbumKSUID   string             `json:"album_ksuid"`
	UserKSUID    string             `json:"user_ksuid"`
	AlbumName    string             `json:"album_name"`
	AlbumType    string             `json:"album_type"`
	IsPublic     bool               `json:"is_public"`
	Description  string             `json:"description"`
	ContentCount int                `json:"content_count"`
	CommentCount int                `json:"comment_count"`
	MarkCount    int                `json:"mark_count"`
	ShareCount   int                `json:"share_count"`
	Contents     []AlbumContentItem `json:"contents"`
	CreatedAt    time.Time          `json:"created_at"`
	UpdatedAt    time.Time          `json:"updated_at"`
}

// DeleteAlbumRequest 删除合集请求
type DeleteAlbumRequest struct {
	UserKSUID  string `json:"-"` // 从JWT中获取
	AlbumKSUID string `json:"-"` // 从路径参数获取
}

// GetUserAlbumsRequest 获取用户合集列表请求
type GetUserAlbumsRequest struct {
	RequestUserKSUID string `json:"-"`                                                  // 请求者KSUID（从JWT中获取）
	UserKSUID        string `json:"-"`                                                  // 目标用户KSUID（从路径参数获取）
	Page             int    `json:"page" form:"page" binding:"min=1"`                   // 页码
	PageSize         int    `json:"page_size" form:"page_size" binding:"min=1,max=100"` // 每页数量
	AlbumType        string `json:"album_type" form:"album_type"`                       // 合集类型过滤（可选）
}

// GetUserAlbumsResponse 获取用户合集列表响应
type GetUserAlbumsResponse struct {
	Albums   []UserAlbumItem `json:"albums"`
	Total    int64           `json:"total"`
	Page     int             `json:"page"`
	PageSize int             `json:"page_size"`
}

// UserAlbumItem 用户合集项
type UserAlbumItem struct {
	AlbumKSUID   string    `json:"album_ksuid"`
	AlbumName    string    `json:"album_name"`
	AlbumType    string    `json:"album_type"`
	IsPublic     bool      `json:"is_public"`
	Description  string    `json:"description"`
	ContentCount int       `json:"content_count"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}
