package model

import (
	"time"
)

// Album 合集模型
type Album struct {
	AlbumKSUID   string     `gorm:"primaryKey;column:album_ksuid;type:varchar(32);not null" json:"album_ksuid" comment:"合集KSUID"`
	UserKSUID    string     `gorm:"column:user_ksuid;type:varchar(32);not null;index" json:"user_ksuid" comment:"用户KSUID"`
	AlbumName    string     `gorm:"column:album_name;type:varchar(100);not null" json:"album_name" comment:"合集名称"`
	AlbumType    string     `gorm:"column:album_type;type:varchar(20);not null" json:"album_type" comment:"合集类型"`
	CoverKSUID   string     `gorm:"column:cover_ksuid;type:varchar(32)" json:"cover_ksuid" comment:"封面KSUID"`
	CoverURL     string     `gorm:"column:cover_url;type:varchar(512)" json:"cover_url" comment:"封面URL"`
	ContentCount int        `gorm:"column:content_count;type:int;default:0" json:"content_count" comment:"内容数量"`
	Description  string     `gorm:"column:description;type:varchar(500)" json:"description" comment:"合集描述"`
	CommentCount int        `gorm:"column:comment_count;type:int;default:0" json:"comment_count" comment:"评论数量"`
	MarkCount    int        `gorm:"column:mark_count;type:int;default:0" json:"mark_count" comment:"收藏数量"`
	ShareCount   int        `gorm:"column:share_count;type:int;default:0" json:"share_count" comment:"分享数量"`
	IsPublic     bool       `gorm:"column:is_public;type:boolean;default:true" json:"is_public" comment:"是否公开"`
	SortOrder    int        `gorm:"column:sort_order;type:int;default:0" json:"sort_order" comment:"排序顺序"`
	CreatedAt    time.Time  `gorm:"column:created_at;not null" json:"created_at" comment:"创建时间"`
	UpdatedAt    time.Time  `gorm:"column:updated_at;not null" json:"updated_at" comment:"更新时间"`
	DeletedAt    *time.Time `gorm:"column:deleted_at;index" json:"deleted_at,omitempty" comment:"删除时间"`
}

func (Album) TableName() string {
	return "albums"
}

// AlbumType 合集类型常量
const (
	AlbumTypeVideo      = "video"       // 视频
	AlbumTypeShortVideo = "short_video" // 短视频
	AlbumTypeAnime      = "anime"       // 动漫
	AlbumTypeNovel      = "novel"       // 小说
)

// IsValidAlbumType 检查合集类型是否有效
func IsValidAlbumType(albumType string) bool {
	validTypes := []string{
		AlbumTypeVideo,
		AlbumTypeShortVideo,
		AlbumTypeAnime,
		AlbumTypeNovel,
	}

	for _, validType := range validTypes {
		if albumType == validType {
			return true
		}
	}
	return false
}
