package providers

import (
	publisher "pxpat-backend/internal/content-cluster/audit-service/messaging/publisher"
	"pxpat-backend/internal/content-cluster/audit-service/types"

	"github.com/rs/zerolog/log"
)

// ProvideMQPublisher 提供MQ发布器
func ProvideMQPublisher(cfg *types.Config) *publisher.Publisher {
	var mqPublisher *publisher.Publisher
	if cfg.RabbitMQ.URL != "" {
		var err error
		mqPublisher, err = publisher.NewPublisher(cfg.RabbitMQ.URL)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize MQ publisher")
			log.Info().Msg("Continuing without MQ message publishing...")
		} else {
			log.Info().Msg("MQ publisher initialized successfully")
		}
	} else {
		log.Info().Msg("RabbitMQ URL not configured, skipping MQ publisher initialization")
	}
	return mqPublisher
}
